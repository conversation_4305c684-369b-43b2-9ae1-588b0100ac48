// 文件名：ini_parser.c
// 功能：INI配置文件解析器，用于config.ini文件解析和配置管理
// 作者：按照宝宝的思路实现
// 版权：Copyright (c) 2024. All rights reserved.

#include "ini_parser.h"
#include "string.h" // 字符串处理函数
#include "stdlib.h" // 标准库函数
#include "ctype.h"  // 字符处理函数

typedef enum // 解析状态枚举
{
    PARSE_IDLE = 0,  // 空闲状态
    PARSE_RATIO = 1, // 在Ratio节
    PARSE_LIMIT = 2  // 在Limit节
} parse_state_t;

ini_status_t ini_trim_string(char *str) // 去除字符串前后空格 参数:字符串指针 返回:处理状态
{
    if (str == NULL) // 参数检查
        return INI_ERROR;

    char *start = str; // 去除前空格
    while (*start && isspace(*start))
        start++;

    char *end = start + strlen(start) - 1; // 去除后空格
    while (end > start && isspace(*end))
        end--;
    *(end + 1) = '\0';

    if (start != str) // 移动字符串到开头
    {
        memmove(str, start, strlen(start) + 1);
    }

    return INI_OK;
}

ini_status_t ini_parse_float(const char *str, float *value) // 解析浮点数 参数:字符串,浮点数指针 返回:解析状态
{
    if (str == NULL || value == NULL) // 参数检查
        return INI_ERROR;

    char *endptr;                      // 转换结束位置指针
    *value = strtof(str, &endptr);     // 字符串转浮点数

    if (endptr == str || *endptr != '\0') // 检查转换是否成功
    {
        return INI_VALUE_ERROR;
    }

    return INI_OK;
}

ini_status_t ini_parse_line(const char *line, ini_config_t *config) // 解析单行数据 参数:行字符串,配置结构体指针 返回:解析状态
{
    if (line == NULL || config == NULL) // 参数检查
        return INI_ERROR;

    static parse_state_t current_state = PARSE_IDLE; // 静态解析状态
    char line_buffer[128];                           // 行缓冲

    strncpy(line_buffer, line, sizeof(line_buffer) - 1); // 复制行内容到缓冲
    line_buffer[sizeof(line_buffer) - 1] = '\0';

    ini_trim_string(line_buffer); // 去除前后空格

    if (strlen(line_buffer) == 0 || line_buffer[0] == ';' || line_buffer[0] == '#') // 跳过空行和注释
    {
        return INI_OK;
    }

    if (line_buffer[0] == '[') // 检查是否为节标识
    {
        char *end_bracket = strchr(line_buffer, ']'); // 查找结束括号
        if (end_bracket == NULL)
            return INI_FORMAT_ERROR;

        *end_bracket = '\0';                      // 截断字符串
        char *section_name = line_buffer + 1;    // 获取节名称
        ini_trim_string(section_name);           // 去除空格

        if (strcmp(section_name, "Ratio") == 0) // 判断节类型
        {
            current_state = PARSE_RATIO; // 进入Ratio节
        }
        else if (strcmp(section_name, "Limit") == 0)
        {
            current_state = PARSE_LIMIT; // 进入Limit节
        }
        else
        {
            current_state = PARSE_IDLE; // 未知节，进入空闲状态
        }

        return INI_OK;
    }

    char *equal_sign = strchr(line_buffer, '='); // 解析键值对，查找等号
    if (equal_sign == NULL)
        return INI_FORMAT_ERROR;

    *equal_sign = '\0';               // 分割等号
    char *key = line_buffer;          // 键名
    char *value = equal_sign + 1;     // 键值

    ini_trim_string(key);             // 去除键名空格
    ini_trim_string(value);           // 去除键值空格

    if (strcmp(key, "Ch0") == 0)      // 检查是否为Ch0键
    {
        float parsed_value;           // 解析浮点数值
        if (ini_parse_float(value, &parsed_value) != INI_OK)
        {
            return INI_VALUE_ERROR;
        }

        if (current_state == PARSE_RATIO) // 根据当前状态保存对应参数
        {
            config->ratio = parsed_value; // 保存变比参数
            config->ratio_found = 1;      // 标记找到ratio
        }
        else if (current_state == PARSE_LIMIT)
        {
            config->limit = parsed_value; // 保存阈值参数
            config->limit_found = 1;      // 标记找到limit
        }
    }

    return INI_OK;
}

ini_status_t ini_parse_file(const char *filename, ini_config_t *config) // 解析INI配置文件 参数:文件名,配置结构体指针 返回:解析状态
{
    if (filename == NULL || config == NULL) // 参数检查
        return INI_ERROR;

    FIL file;                // FATFS文件对象
    FRESULT fr;              // 文件处理结果
    char line_buffer[128];   // 行缓冲
    UINT bytes_read;         // 读取字节数

    config->ratio = 0.0f;    // 初始化配置结构体
    config->limit = 0.0f;
    config->ratio_found = 0;
    config->limit_found = 0;

    fr = f_open(&file, filename, FA_READ); // 打开文件
    if (fr != FR_OK)
    {
        return INI_FILE_NOT_FOUND;
    }

    while (f_gets(line_buffer, sizeof(line_buffer), &file) != NULL) // 逐行读取文件
    {
        ini_status_t status = ini_parse_line(line_buffer, config); // 解析每一行
        if (status != INI_OK)                                      // 解析出错处理
        {
            f_close(&file);
            return status;
        }
    }

    f_close(&file); // 关闭文件

    return INI_OK;
}
