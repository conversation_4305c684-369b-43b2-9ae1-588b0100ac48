File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.294357%,12945,96,12394,551,0,96
usart_app.o,15.102003%,11304,2445,10906,325,73,2372
ff.o,10.157513%,7603,518,7456,141,6,512
sdio_sdcard.o,9.579030%,7170,68,7134,0,36,32
oled.o,5.311886%,3976,22,1242,2712,22,0
btod.o,2.875045%,2152,0,2152,0,0,0
mcu_cmic_gd32f470vet6.o,2.335306%,1748,592,1728,0,20,572
fz_wm.l,2.033373%,1522,0,1506,16,0,0
gd32f4xx_dma.o,1.758160%,1316,0,1316,0,0,0
scanf_fp.o,1.699376%,1272,0,1272,0,0,0
system_selftest.o,1.437523%,1076,492,1076,0,0,492
_printf_fp_dec.o,1.408131%,1054,0,1054,0,0,0
gd25qxx.o,1.322628%,990,0,990,0,0,0
_scanf.o,1.181013%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.154293%,864,0,864,0,0,0
perf_counter.o,1.132917%,848,64,780,4,64,0
_printf_fp_hex.o,1.071462%,802,0,764,38,0,0
scanf_hexfp.o,1.068790%,800,0,800,0,0,0
m_wm.l,1.039398%,778,0,778,0,0,0
gd32f4xx_usart.o,0.977943%,732,0,732,0,0,0
system_gd32f4xx.o,0.932519%,698,4,694,0,4,0
gd32f4xx_adc.o,0.873736%,654,0,654,0,0,0
gd32f4xx_sdio.o,0.839000%,628,0,628,0,0,0
gd32f4xx_timer.o,0.785561%,588,0,588,0,0,0
gd32f4xx_i2c.o,0.662650%,496,0,496,0,0,0
startup_gd32f450_470.o,0.657306%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.646618%,484,0,484,0,0,0
task_key.o,0.558443%,418,6,412,0,6,0
__printf_flags_ss_wp.o,0.546419%,409,0,392,17,0,0
bigflt0.o,0.502331%,376,0,228,148,0,0
dmul.o,0.454236%,340,0,340,0,0,0
_scanf_int.o,0.443548%,332,0,332,0,0,0
lc_ctype_c.o,0.422172%,316,0,44,272,0,0
diskio.o,0.422172%,316,0,316,0,0,0
scanf_infnan.o,0.411484%,308,0,308,0,0,0
narrow.o,0.355373%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.350029%,262,0,262,0,0,0
lludivv7m.o,0.317965%,238,0,238,0,0,0
ldexp.o,0.304605%,228,0,228,0,0,0
gd32f4xx_misc.o,0.288573%,216,0,216,0,0,0
gd32f4xx_dac.o,0.264526%,198,0,198,0,0,0
_printf_wctomb.o,0.261854%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.251166%,188,0,148,40,0,0
scheduler.o,0.251166%,188,88,100,0,88,0
_printf_intcommon.o,0.237806%,178,0,178,0,0,0
systick.o,0.224446%,168,4,164,0,4,0
strtod.o,0.219102%,164,0,164,0,0,0
dnaninf.o,0.208414%,156,0,156,0,0,0
perfc_port_default.o,0.205742%,154,0,154,0,0,0
frexp.o,0.187038%,140,0,140,0,0,0
fnaninf.o,0.187038%,140,0,140,0,0,0
rt_memcpy_v6.o,0.184366%,138,0,138,0,0,0
lludiv10.o,0.184366%,138,0,138,0,0,0
rt_memmove_v6.o,0.176350%,132,0,132,0,0,0
strcmpv7m.o,0.171006%,128,0,128,0,0,0
_printf_fp_infnan.o,0.171006%,128,0,128,0,0,0
gd32f4xx_it.o,0.171006%,128,0,128,0,0,0
_printf_longlong_dec.o,0.165662%,124,0,124,0,0,0
rt_memmove_w.o,0.162990%,122,0,122,0,0,0
dleqf.o,0.160318%,120,0,120,0,0,0
deqf.o,0.160318%,120,0,120,0,0,0
_printf_dec.o,0.160318%,120,0,120,0,0,0
_printf_oct_int_ll.o,0.149631%,112,0,112,0,0,0
drleqf.o,0.144287%,108,0,108,0,0,0
device_info.o,0.144287%,108,52,108,0,0,52
gd32f4xx_spi.o,0.138943%,104,0,104,0,0,0
retnan.o,0.133599%,100,0,100,0,0,0
rt_memcpy_w.o,0.133599%,100,0,100,0,0,0
d2f.o,0.130927%,98,0,98,0,0,0
rtc_app.o,0.128255%,96,0,96,0,0,0
main.o,0.125583%,94,0,94,0,0,0
scalbn.o,0.122911%,92,0,92,0,0,0
__dczerorl2.o,0.120239%,90,0,90,0,0,0
f2d.o,0.114895%,86,0,86,0,0,0
strncpy.o,0.114895%,86,0,86,0,0,0
_printf_str.o,0.109551%,82,0,82,0,0,0
rt_memclr_w.o,0.104207%,78,0,78,0,0,0
_printf_pad.o,0.104207%,78,0,78,0,0,0
led_app.o,0.104207%,78,2,76,0,2,0
sys_stackheap_outer.o,0.098863%,74,0,74,0,0,0
strcpy.o,0.096191%,72,0,72,0,0,0
llsdiv.o,0.096191%,72,0,72,0,0,0
lc_numeric_c.o,0.096191%,72,0,44,28,0,0
unicode.o,0.096191%,72,0,72,0,0,0
rt_memclr.o,0.090847%,68,0,68,0,0,0
dunder.o,0.085503%,64,0,64,0,0,0
_wcrtomb.o,0.085503%,64,0,64,0,0,0
_sgetc.o,0.085503%,64,0,64,0,0,0
strlen.o,0.082831%,62,0,62,0,0,0
__0sscanf.o,0.080159%,60,0,60,0,0,0
__2snprintf.o,0.074815%,56,0,56,0,0,0
vsnprintf.o,0.069471%,52,0,52,0,0,0
__scatter.o,0.069471%,52,0,52,0,0,0
fpclassify.o,0.064127%,48,0,48,0,0,0
trapv.o,0.064127%,48,0,48,0,0,0
_printf_char_common.o,0.064127%,48,0,48,0,0,0
scanf_char.o,0.058783%,44,0,44,0,0,0
_printf_wchar.o,0.058783%,44,0,44,0,0,0
_printf_char.o,0.058783%,44,0,44,0,0,0
_printf_charcount.o,0.053439%,40,0,40,0,0,0
llshl.o,0.050768%,38,0,38,0,0,0
libinit2.o,0.050768%,38,0,38,0,0,0
strstr.o,0.048096%,36,0,36,0,0,0
init_aeabi.o,0.048096%,36,0,36,0,0,0
_printf_truncate.o,0.048096%,36,0,36,0,0,0
strtof.o,0.042752%,32,0,32,0,0,0
systick_wrapper_ual.o,0.042752%,32,0,32,0,0,0
_chval.o,0.037408%,28,0,28,0,0,0
__scatter_zi.o,0.037408%,28,0,28,0,0,0
strtof.o,0.034736%,26,0,26,0,0,0
dcmpi.o,0.032064%,24,0,24,0,0,0
_rserrno.o,0.029392%,22,0,22,0,0,0
strchr.o,0.026720%,20,0,20,0,0,0
gd32f4xx_pmu.o,0.026720%,20,0,20,0,0,0
adc_app.o,0.026720%,20,0,20,0,0,0
isspace.o,0.024048%,18,0,18,0,0,0
exit.o,0.024048%,18,0,18,0,0,0
fpconst.o,0.021376%,16,0,0,16,0,0
dcheck1.o,0.021376%,16,0,16,0,0,0
rt_ctype_table.o,0.021376%,16,0,16,0,0,0
_snputc.o,0.021376%,16,0,16,0,0,0
__printf_wp.o,0.018704%,14,0,14,0,0,0
sd_app.o,0.018704%,14,1436,14,0,0,1436
dretinf.o,0.016032%,12,0,12,0,0,0
sys_exit.o,0.016032%,12,0,12,0,0,0
__rtentry2.o,0.016032%,12,0,12,0,0,0
fretinf.o,0.013360%,10,0,10,0,0,0
fpinit.o,0.013360%,10,0,10,0,0,0
rtexit2.o,0.013360%,10,0,10,0,0,0
_sputc.o,0.013360%,10,0,10,0,0,0
_printf_ll.o,0.013360%,10,0,10,0,0,0
_printf_l.o,0.013360%,10,0,10,0,0,0
scanf2.o,0.010688%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010688%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010688%,8,0,8,0,0,0
libspace.o,0.010688%,8,96,8,0,0,96
__main.o,0.010688%,8,0,8,0,0,0
oled_app.o,0.010688%,8,0,8,0,0,0
istatus.o,0.008016%,6,0,6,0,0,0
heapauxi.o,0.008016%,6,0,6,0,0,0
_printf_x.o,0.008016%,6,0,6,0,0,0
_printf_u.o,0.008016%,6,0,6,0,0,0
_printf_s.o,0.008016%,6,0,6,0,0,0
_printf_p.o,0.008016%,6,0,6,0,0,0
_printf_o.o,0.008016%,6,0,6,0,0,0
_printf_n.o,0.008016%,6,0,6,0,0,0
_printf_ls.o,0.008016%,6,0,6,0,0,0
_printf_llx.o,0.008016%,6,0,6,0,0,0
_printf_llu.o,0.008016%,6,0,6,0,0,0
_printf_llo.o,0.008016%,6,0,6,0,0,0
_printf_lli.o,0.008016%,6,0,6,0,0,0
_printf_lld.o,0.008016%,6,0,6,0,0,0
_printf_lc.o,0.008016%,6,0,6,0,0,0
_printf_i.o,0.008016%,6,0,6,0,0,0
_printf_g.o,0.008016%,6,0,6,0,0,0
_printf_f.o,0.008016%,6,0,6,0,0,0
_printf_e.o,0.008016%,6,0,6,0,0,0
_printf_d.o,0.008016%,6,0,6,0,0,0
_printf_c.o,0.008016%,6,0,6,0,0,0
_printf_a.o,0.008016%,6,0,6,0,0,0
__rtentry4.o,0.008016%,6,0,6,0,0,0
scanf1.o,0.005344%,4,0,4,0,0,0
printf2.o,0.005344%,4,0,4,0,0,0
printf1.o,0.005344%,4,0,4,0,0,0
_printf_percent_end.o,0.005344%,4,0,4,0,0,0
use_no_semi.o,0.002672%,2,0,2,0,0,0
rtexit.o,0.002672%,2,0,2,0,0,0
libshutdown2.o,0.002672%,2,0,2,0,0,0
libshutdown.o,0.002672%,2,0,2,0,0,0
libinit.o,0.002672%,2,0,2,0,0,0
