File_name,flash percent,flash,ram,Code,RO_data,RW_data,ZI_data
c_w.l,17.613020%,12845,96,12294,551,0,96
usart_app.o,13.731163%,10014,2421,9904,61,49,2372
ff.o,9.846563%,7181,518,7034,141,6,512
sdio_sdcard.o,9.831480%,7170,68,7134,0,36,32
oled.o,5.451878%,3976,22,1242,2712,22,0
btod.o,2.950815%,2152,0,2152,0,0,0
mcu_cmic_gd32f470vet6.o,2.396852%,1748,592,1728,0,20,572
fz_wm.l,2.086961%,1522,0,1506,16,0,0
gd32f4xx_dma.o,1.804495%,1316,0,1316,0,0,0
scanf_fp.o,1.744162%,1272,0,1272,0,0,0
system_selftest.o,1.458953%,1064,492,1064,0,0,492
_printf_fp_dec.o,1.445241%,1054,0,1054,0,0,0
gd25qxx.o,1.357485%,990,0,990,0,0,0
_scanf.o,1.212138%,884,0,884,0,0,0
gd32f4xx_rcu.o,1.184714%,864,0,864,0,0,0
perf_counter.o,1.162775%,848,64,780,4,64,0
_printf_fp_hex.o,1.099700%,802,0,764,38,0,0
scanf_hexfp.o,1.096957%,800,0,800,0,0,0
m_wm.l,1.066791%,778,0,778,0,0,0
gd32f4xx_usart.o,1.003716%,732,0,732,0,0,0
system_gd32f4xx.o,0.957095%,698,4,694,0,4,0
gd32f4xx_adc.o,0.896763%,654,0,654,0,0,0
gd32f4xx_sdio.o,0.861112%,628,0,628,0,0,0
gd32f4xx_timer.o,0.806264%,588,0,588,0,0,0
gd32f4xx_i2c.o,0.680114%,496,0,496,0,0,0
startup_gd32f450_470.o,0.674629%,492,2048,64,428,0,2048
gd32f4xx_rtc.o,0.663659%,484,0,484,0,0,0
task_key.o,0.562191%,410,6,404,0,6,0
__printf_flags_ss_wp.o,0.560819%,409,0,392,17,0,0
bigflt0.o,0.515570%,376,0,228,148,0,0
dmul.o,0.466207%,340,0,340,0,0,0
_scanf_int.o,0.455237%,332,0,332,0,0,0
lc_ctype_c.o,0.433298%,316,0,44,272,0,0
diskio.o,0.433298%,316,0,316,0,0,0
scanf_infnan.o,0.422329%,308,0,308,0,0,0
narrow.o,0.364738%,266,0,266,0,0,0
gd32f4xx_gpio.o,0.359254%,262,0,262,0,0,0
lludivv7m.o,0.326345%,238,0,238,0,0,0
ldexp.o,0.312633%,228,0,228,0,0,0
gd32f4xx_misc.o,0.296178%,216,0,216,0,0,0
gd32f4xx_dac.o,0.271497%,198,0,198,0,0,0
_printf_wctomb.o,0.268755%,196,0,188,8,0,0
_printf_hex_int_ll_ptr.o,0.257785%,188,0,148,40,0,0
_printf_intcommon.o,0.244073%,178,0,178,0,0,0
scheduler.o,0.241331%,176,76,100,0,76,0
systick.o,0.230361%,168,4,164,0,4,0
strtod.o,0.224876%,164,0,164,0,0,0
dnaninf.o,0.213907%,156,0,156,0,0,0
perfc_port_default.o,0.211164%,154,0,154,0,0,0
frexp.o,0.191968%,140,0,140,0,0,0
fnaninf.o,0.191968%,140,0,140,0,0,0
rt_memcpy_v6.o,0.189225%,138,0,138,0,0,0
lludiv10.o,0.189225%,138,0,138,0,0,0
rt_memmove_v6.o,0.180998%,132,0,132,0,0,0
strcmpv7m.o,0.175513%,128,0,128,0,0,0
_printf_fp_infnan.o,0.175513%,128,0,128,0,0,0
gd32f4xx_it.o,0.175513%,128,0,128,0,0,0
_printf_longlong_dec.o,0.170028%,124,0,124,0,0,0
rt_memmove_w.o,0.167286%,122,0,122,0,0,0
dleqf.o,0.164544%,120,0,120,0,0,0
deqf.o,0.164544%,120,0,120,0,0,0
_printf_dec.o,0.164544%,120,0,120,0,0,0
main.o,0.159059%,116,0,116,0,0,0
_printf_oct_int_ll.o,0.153574%,112,0,112,0,0,0
drleqf.o,0.148089%,108,0,108,0,0,0
device_info.o,0.148089%,108,52,108,0,0,52
gd32f4xx_spi.o,0.142604%,104,0,104,0,0,0
retnan.o,0.137120%,100,0,100,0,0,0
rt_memcpy_w.o,0.137120%,100,0,100,0,0,0
d2f.o,0.134377%,98,0,98,0,0,0
rtc_app.o,0.131635%,96,0,96,0,0,0
scalbn.o,0.126150%,92,0,92,0,0,0
f2d.o,0.117923%,86,0,86,0,0,0
strncpy.o,0.117923%,86,0,86,0,0,0
_printf_str.o,0.112438%,82,0,82,0,0,0
rt_memclr_w.o,0.106953%,78,0,78,0,0,0
_printf_pad.o,0.106953%,78,0,78,0,0,0
led_app.o,0.106953%,78,2,76,0,2,0
sys_stackheap_outer.o,0.101469%,74,0,74,0,0,0
strcpy.o,0.098726%,72,0,72,0,0,0
llsdiv.o,0.098726%,72,0,72,0,0,0
lc_numeric_c.o,0.098726%,72,0,44,28,0,0
unicode.o,0.098726%,72,0,72,0,0,0
rt_memclr.o,0.093241%,68,0,68,0,0,0
dunder.o,0.087757%,64,0,64,0,0,0
_wcrtomb.o,0.087757%,64,0,64,0,0,0
_sgetc.o,0.087757%,64,0,64,0,0,0
strlen.o,0.085014%,62,0,62,0,0,0
__0sscanf.o,0.082272%,60,0,60,0,0,0
__2snprintf.o,0.076787%,56,0,56,0,0,0
vsnprintf.o,0.071302%,52,0,52,0,0,0
__scatter.o,0.071302%,52,0,52,0,0,0
fpclassify.o,0.065817%,48,0,48,0,0,0
trapv.o,0.065817%,48,0,48,0,0,0
_printf_char_common.o,0.065817%,48,0,48,0,0,0
scanf_char.o,0.060333%,44,0,44,0,0,0
_printf_wchar.o,0.060333%,44,0,44,0,0,0
_printf_char.o,0.060333%,44,0,44,0,0,0
_printf_charcount.o,0.054848%,40,0,40,0,0,0
llshl.o,0.052105%,38,0,38,0,0,0
libinit2.o,0.052105%,38,0,38,0,0,0
init_aeabi.o,0.049363%,36,0,36,0,0,0
_printf_truncate.o,0.049363%,36,0,36,0,0,0
strtof.o,0.043878%,32,0,32,0,0,0
systick_wrapper_ual.o,0.043878%,32,0,32,0,0,0
_chval.o,0.038394%,28,0,28,0,0,0
__scatter_zi.o,0.038394%,28,0,28,0,0,0
strtof.o,0.035651%,26,0,26,0,0,0
__scatter_copy.o,0.035651%,26,0,26,0,0,0
dcmpi.o,0.032909%,24,0,24,0,0,0
_rserrno.o,0.030166%,22,0,22,0,0,0
strchr.o,0.027424%,20,0,20,0,0,0
gd32f4xx_pmu.o,0.027424%,20,0,20,0,0,0
adc_app.o,0.027424%,20,0,20,0,0,0
isspace.o,0.024682%,18,0,18,0,0,0
exit.o,0.024682%,18,0,18,0,0,0
fpconst.o,0.021939%,16,0,0,16,0,0
dcheck1.o,0.021939%,16,0,16,0,0,0
rt_ctype_table.o,0.021939%,16,0,16,0,0,0
_snputc.o,0.021939%,16,0,16,0,0,0
__printf_wp.o,0.019197%,14,0,14,0,0,0
sd_app.o,0.019197%,14,1436,14,0,0,1436
dretinf.o,0.016454%,12,0,12,0,0,0
sys_exit.o,0.016454%,12,0,12,0,0,0
__rtentry2.o,0.016454%,12,0,12,0,0,0
fretinf.o,0.013712%,10,0,10,0,0,0
fpinit.o,0.013712%,10,0,10,0,0,0
rtexit2.o,0.013712%,10,0,10,0,0,0
_sputc.o,0.013712%,10,0,10,0,0,0
_printf_ll.o,0.013712%,10,0,10,0,0,0
_printf_l.o,0.013712%,10,0,10,0,0,0
scanf2.o,0.010970%,8,0,8,0,0,0
rt_locale_intlibspace.o,0.010970%,8,0,8,0,0,0
rt_errno_addr_intlibspace.o,0.010970%,8,0,8,0,0,0
libspace.o,0.010970%,8,96,8,0,0,96
__main.o,0.010970%,8,0,8,0,0,0
oled_app.o,0.010970%,8,0,8,0,0,0
istatus.o,0.008227%,6,0,6,0,0,0
heapauxi.o,0.008227%,6,0,6,0,0,0
_printf_x.o,0.008227%,6,0,6,0,0,0
_printf_u.o,0.008227%,6,0,6,0,0,0
_printf_s.o,0.008227%,6,0,6,0,0,0
_printf_p.o,0.008227%,6,0,6,0,0,0
_printf_o.o,0.008227%,6,0,6,0,0,0
_printf_n.o,0.008227%,6,0,6,0,0,0
_printf_ls.o,0.008227%,6,0,6,0,0,0
_printf_llx.o,0.008227%,6,0,6,0,0,0
_printf_llu.o,0.008227%,6,0,6,0,0,0
_printf_llo.o,0.008227%,6,0,6,0,0,0
_printf_lli.o,0.008227%,6,0,6,0,0,0
_printf_lld.o,0.008227%,6,0,6,0,0,0
_printf_lc.o,0.008227%,6,0,6,0,0,0
_printf_i.o,0.008227%,6,0,6,0,0,0
_printf_g.o,0.008227%,6,0,6,0,0,0
_printf_f.o,0.008227%,6,0,6,0,0,0
_printf_e.o,0.008227%,6,0,6,0,0,0
_printf_d.o,0.008227%,6,0,6,0,0,0
_printf_c.o,0.008227%,6,0,6,0,0,0
_printf_a.o,0.008227%,6,0,6,0,0,0
__rtentry4.o,0.008227%,6,0,6,0,0,0
scanf1.o,0.005485%,4,0,4,0,0,0
printf2.o,0.005485%,4,0,4,0,0,0
printf1.o,0.005485%,4,0,4,0,0,0
_printf_percent_end.o,0.005485%,4,0,4,0,0,0
use_no_semi.o,0.002742%,2,0,2,0,0,0
rtexit.o,0.002742%,2,0,2,0,0,0
libshutdown2.o,0.002742%,2,0,2,0,0,0
libshutdown.o,0.002742%,2,0,2,0,0,0
libinit.o,0.002742%,2,0,2,0,0,0
