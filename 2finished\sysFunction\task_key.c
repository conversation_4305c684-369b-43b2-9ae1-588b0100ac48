#include "mcu_cmic_gd32f470vet6.h"
#include "usart_app.h"  // 包含日志记录函数声明
uint8_t key_val,key_old,key_down,key_up;
uint8_t country=5;
uint8_t trager_flag;

// 外部变量声明
extern uint8_t uart_flag; // 采样控制标志，来自led_app.c


uint8_t key_read()
{
	uint8_t temp = 0;
	if(gpio_input_bit_get(KEYA_PORT,KEY1_PIN ) == RESET)
		temp=1;
	if(gpio_input_bit_get(KEYE_PORT, KEY2_PIN) == RESET)
		temp=2;
	if(gpio_input_bit_get(KEYE_PORT, KEY3_PIN) == RESET)
		temp=3;
	if(gpio_input_bit_get(KEYE_PORT, KEY4_PIN) == RESET)
		temp=4;
		return temp;
}

void key_task(void)
{
	key_val = key_read();
	key_down = key_val & (key_old ^ key_val);
	key_up = ~key_val & (key_old ^ key_val);
	key_old = key_val;
	if(key_down>1&&key_down<=4)
	{
		country=(key_down-1)*5; // KEY2=5s, KEY3=10s, KEY4=15s
		save_sample_cycle_to_flash(country); // 保存周期配置到Flash
		write_log_entry("cycle switch to %ds (key press)", country); // 记录周期切换日志
		my_printf(DEBUG_USART, "sample cycle adjust: %ds\r\n", country); // 输出周期调整信息
	}
	
	if(key_down==1)
	{
		trager_flag^=1;
		uart_flag^=1; // KEY1按键切换采样状态
		// 记录按键采样控制日志
		if(uart_flag) {
			write_log_entry("sample start - cycle %ds (key press)", country);
		} else {
			write_log_entry("sample stop (key press)");
		}
	}
		
	
}

